<template>
  <div class="order-create-container">
    <Toast />

    <!-- Critical Error Boundary -->
    <div v-if="criticalError" class="fixed inset-0 bg-red-900 bg-opacity-75 flex justify-center items-center z-50">
      <div class="bg-white p-8 rounded-lg shadow-xl text-center max-w-md">
        <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-4"></i>
        <h3 class="text-xl font-bold text-red-700 mb-2">Lỗi nghiêm trọng</h3>
        <p class="text-surface-700 mb-4">{{ criticalError.message }}</p>
        <p class="text-sm text-surface-500 mb-6">Ngữ cảnh: {{ criticalError.context }}</p>
        <div class="space-y-2">
          <Button label="Tải lại trang" icon="pi pi-refresh" severity="danger"
            @click="safePageReload" class="w-full" />
          <Button label="Quay về danh sách" icon="pi pi-arrow-left" outlined
            @click="() => router.push('/orders')" class="w-full" />
        </div>
      </div>
    </div>

    <!-- Updating Progress -->
    <div v-if="updating" class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg text-center">
        <ProgressSpinner />
        <p class="mt-4 text-lg font-medium">Đang cập nhật đơn hàng...</p>
        <p class="text-sm text-surface-500">Vui lòng đợi trong giây lát</p>
      </div>
    </div>

    <!-- Page Header -->
    <div class="card mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <i class="pi pi-pencil text-lg text-primary"></i>
          </div>
          <div>
            <h1 class="font-semibold text-xl text-surface-900 m-0">Chỉnh sửa đơn hàng</h1>
            <p class="text-surface-500 text-sm mt-1 mb-0">
              Cập nhật thông tin đơn hàng {{ orderData?.maHoaDon }}
            </p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <!-- Order Expiration Warning -->
          <div v-if="criticalExpiringOrders.length > 0"
            class="flex items-center gap-2 px-3 py-1 rounded-lg border bg-red-50 border-red-200 text-red-700">
            <i class="pi pi-exclamation-triangle text-red-500 text-xs"></i>
            <span class="text-xs font-medium">
              {{ criticalExpiringOrders.length }} đơn hàng sắp hết hạn
            </span>
          </div>

          <!-- General Expiration Updates -->
          <div v-else-if="hasExpirationUpdates"
            class="flex items-center gap-2 px-3 py-1 rounded-lg border bg-orange-50 border-orange-200 text-orange-700">
            <i class="pi pi-clock text-orange-500 text-xs"></i>
            <span class="text-xs font-medium">Có cập nhật hết hạn</span>
          </div>

          <Button label="Quay lại" icon="pi pi-arrow-left" outlined @click="router.push('/orders')" />
        </div>
      </div>
    </div>

    <!-- Order Actions Bar -->
    <div v-if="orderData && !loading && !error" class="card mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
            <i class="pi pi-file text-primary"></i>
          </div>
          <div>
            <span class="font-medium text-sm">{{ orderData.maHoaDon }}</span>
            <Badge v-if="orderData.sanPhamList?.length > 0" :value="orderData.sanPhamList.length" severity="info" size="small" class="ml-2" />
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center gap-2">
          <Button label="Chọn sản phẩm" icon="pi pi-plus" severity="primary" @click="showProductSelectionDialog"
            v-tooltip.top="'Chọn sản phẩm từ danh sách'" />
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="card">
      <div class="text-center py-12">
        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-4"></i>
        <h3 class="text-xl font-semibold text-surface-600 mb-2">Đang tải thông tin đơn hàng...</h3>
        <p class="text-surface-500">Vui lòng chờ trong giây lát</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="card">
      <div class="text-center py-12">
        <i class="pi pi-exclamation-triangle text-6xl text-red-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-red-600 mb-2">Không thể tải thông tin đơn hàng</h3>
        <p class="text-surface-500 mb-6">{{ error }}</p>
        <Button label="Thử lại" icon="pi pi-refresh" @click="loadOrderData" :loading="loading" />
      </div>
    </div>

    <!-- Main Order Edit Interface -->
    <div v-else-if="orderData" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column: Product Selection & Order Items -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Order Items -->
        <div class="card border border-surface-200">
          <div class="font-semibold text-lg mb-4 flex items-center justify-between">
            <div class="flex items-center gap-2">
              <i class="pi pi-shopping-cart text-primary"></i>
              Sản phẩm trong đơn hàng
            </div>
            <div class="flex items-center gap-2">
              <Badge v-if="orderData?.sanPhamList?.length > 0" :value="orderData.sanPhamList.length" severity="info" />
            </div>
          </div>

          <!-- Product List -->
          <div v-if="orderData?.sanPhamList?.length > 0" class="space-y-3">
            <div v-for="(item, index) in orderData.sanPhamList" :key="index"
              class="flex items-center gap-4 p-4 border border-surface-200 rounded-lg">
              <!-- Product Image -->
              <div class="w-16 h-16 bg-surface-100 rounded-lg flex items-center justify-center overflow-hidden">
                <img v-if="item.sanPhamChiTiet?.sanPham?.hinhAnh" :src="getImageUrl(item.sanPhamChiTiet.sanPham.hinhAnh)"
                  :alt="item.sanPhamChiTiet?.sanPham?.tenSanPham" class="w-full h-full object-cover" />
                <i v-else class="pi pi-image text-2xl text-surface-400"></i>
              </div>

              <!-- Product Info -->
              <div class="flex-1">
                <h4 class="font-medium text-surface-900 mb-1">
                  {{ item.sanPhamChiTiet?.sanPham?.tenSanPham }}
                </h4>
                <p class="text-sm text-surface-600 mb-2">
                  {{ formatVariantName(item.sanPhamChiTiet) }}
                </p>
                <div class="flex items-center gap-4 text-sm">
                  <span class="text-surface-600">Số lượng: {{ item.soLuong }}</span>
                  <span class="text-surface-600">Đơn giá: {{ formatCurrency(item.donGia) }}</span>
                  <span class="font-medium text-primary">Thành tiền: {{ formatCurrency(item.thanhTien) }}</span>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex items-center gap-2">
                <Button icon="pi pi-pencil" text rounded size="small" severity="info" @click="editProduct(item, index)"
                  v-tooltip.top="'Chỉnh sửa'" />
                <Button icon="pi pi-trash" text rounded size="small" severity="danger" @click="removeProduct(index)"
                  v-tooltip.top="'Xóa'" />
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-8">
            <i class="pi pi-shopping-cart text-4xl text-surface-300 mb-3"></i>
            <p class="text-surface-500">Chưa có sản phẩm nào trong đơn hàng</p>
          </div>

          <!-- Add Product Button -->
          <div class="mt-4 pt-4 border-t border-surface-200">
            <Button label="Thêm sản phẩm" icon="pi pi-plus" outlined class="w-full" @click="showProductSelectionDialog" />
          </div>
        </div>
      </div>

      <!-- Right Column: Order Summary & Actions -->
      <div class="space-y-6">
        <!-- Customer Selection -->
        <div class="card border border-surface-200">
          <div class="font-semibold text-lg mb-4 flex items-center justify-between">
            <div class="flex items-center gap-2">
              <i class="pi pi-user text-primary"></i>
              Khách hàng
            </div>
            <Button label="Thêm nhanh" icon="pi pi-user-plus" size="small" severity="success" outlined
              @click="fastCustomerDialogVisible = true" />
          </div>

          <!-- Customer Search -->
          <div class="mb-4">
            <AutoComplete v-model="selectedCustomer" :suggestions="customerSuggestions" @complete="searchCustomers"
              @item-select="onCustomerSelect" :optionLabel="getCustomerDisplayLabel"
              placeholder="Tìm kiếm khách hàng (tên hoặc số điện thoại)..." fluid>
              <template #item="{ item }">
                <div class="flex items-center gap-2 p-2">
                  <Avatar :label="item.hoTen?.charAt(0)" size="small" />
                  <div>
                    <div class="font-medium">{{ item.hoTen }} - {{ item.soDienThoai }}</div>
                    <div class="text-sm text-surface-500">{{ item.email || 'Không có email' }}</div>
                  </div>
                </div>
              </template>
            </AutoComplete>
          </div>

          <!-- Selected Customer Display -->
          <div v-if="orderData?.khachHang" class="p-3 border rounded-lg bg-surface-50">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <Avatar :label="orderData.khachHang.hoTen?.charAt(0)" size="small" />
                <div>
                  <div class="font-semibold text-sm">{{ orderData.khachHang.hoTen }}</div>
                  <div class="text-xs text-surface-500">{{ orderData.khachHang.soDienThoai }}</div>
                </div>
              </div>
              <Button icon="pi pi-times" text rounded size="small" @click="clearCustomer"
                class="text-surface-400 hover:text-red-500" />
            </div>
          </div>

          <!-- Walk-in Customer Note -->
          <div v-else class="text-center py-3 text-surface-500">
            <i class="pi pi-user-plus text-lg mb-1"></i>
            <p class="text-xs">Khách hàng vãng lai</p>
          </div>
        </div>

        <!-- Delivery Options -->
        <div class="card border border-surface-200">
          <div class="font-semibold text-lg mb-4 flex items-center gap-2">
            <i class="pi pi-truck text-primary"></i>
            Giao hàng
          </div>

          <div class="flex items-center justify-between mb-4">
            <label class="font-medium">Giao hàng tận nơi</label>
            <ToggleButton v-model="orderData.giaohang" onLabel="Có" offLabel="Không" @change="onDeliveryToggle" />
          </div>

          <!-- Recipient Information Form (when delivery is enabled) -->
          <div v-if="orderData?.giaohang" class="space-y-4">
            <!-- Recipient Information Header -->
            <div class="border-t pt-4">
              <div class="font-semibold text-base mb-3 flex items-center gap-2">
                <i class="pi pi-user-plus text-blue-600"></i>
                <span class="text-blue-800">Thông tin người nhận</span>
              </div>

              <!-- Recipient Name -->
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">
                  Tên người nhận <span class="text-red-500">*</span>
                </label>
                <AutoComplete v-model="recipientInfo.hoTen" :suggestions="recipientNameSuggestions"
                  @complete="searchRecipientByName" @item-select="onRecipientNameSelect" optionLabel="hoTen"
                  placeholder="Nhập tên người nhận..." class="w-full" :class="{ 'p-invalid': recipientErrors.hoTen }"
                  :loading="searchingRecipient" fluid>
                  <template #item="{ item }">
                    <div class="flex items-center gap-2 p-2">
                      <Avatar :label="item.hoTen?.charAt(0)" size="small" />
                      <div>
                        <div class="font-medium">{{ item.hoTen }}</div>
                        <div class="text-sm text-surface-500">{{
                          item.soDienThoai || 'Không có SĐT'
                          }}</div>
                      </div>
                    </div>
                  </template>
                </AutoComplete>
                <small v-if="recipientErrors.hoTen" class="p-error">{{
                  recipientErrors.hoTen
                  }}</small>
              </div>

              <!-- Recipient Phone -->
              <div class="mb-4">
                <label class="block text-sm font-medium mb-1">
                  Số điện thoại người nhận <span class="text-red-500">*</span>
                </label>
                <AutoComplete v-model="recipientInfo.soDienThoai" :suggestions="recipientPhoneSuggestions"
                  @complete="searchRecipientByPhone" @item-select="onRecipientPhoneSelect" optionLabel="soDienThoai"
                  placeholder="Nhập số điện thoại người nhận..." class="w-full"
                  :class="{ 'p-invalid': recipientErrors.soDienThoai }" :loading="searchingRecipient" fluid>
                  <template #item="{ item }">
                    <div class="flex items-center gap-2 p-2">
                      <Avatar :label="item.hoTen?.charAt(0)" size="small" />
                      <div>
                        <div class="font-medium">{{ item.hoTen || 'Không có tên' }}</div>
                        <div class="text-sm text-surface-500">{{ item.soDienThoai }}</div>
                      </div>
                    </div>
                  </template>
                </AutoComplete>
                <small v-if="recipientErrors.soDienThoai" class="p-error">{{
                  recipientErrors.soDienThoai
                  }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Voucher Section -->
        <div class="card border border-surface-200">
          <div class="font-semibold text-lg mb-4 flex items-center gap-2">
            <i class="pi pi-ticket text-primary"></i>
            Voucher giảm giá
          </div>

          <!-- Applied Vouchers -->
          <div v-if="orderData?.voucherList?.length > 0" class="space-y-2 mb-4">
            <div v-for="(voucher, index) in orderData.voucherList" :key="index"
              class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div class="flex items-center gap-3">
                <i class="pi pi-ticket text-green-600"></i>
                <div>
                  <div class="font-medium text-sm">{{ voucher.tenVoucher }}</div>
                  <div class="text-xs text-green-600">Giảm {{ formatCurrency(voucher.giaTriGiam) }}</div>
                </div>
              </div>
              <Button icon="pi pi-times" text rounded size="small" severity="danger" @click="removeVoucher(index)"
                v-tooltip.top="'Xóa voucher'" />
            </div>
          </div>

          <!-- Voucher Search -->
          <div class="space-y-3">
            <AutoComplete v-model="selectedVoucher" :suggestions="voucherSuggestions" @complete="searchVouchers"
              @item-select="onVoucherSelect" optionLabel="tenVoucher" placeholder="Tìm kiếm voucher..."
              class="w-full" />
            <Button label="Tìm voucher tốt nhất" icon="pi pi-search" outlined size="small" class="w-full"
              @click="findBestVoucher" :loading="loadingBestVoucher" />
          </div>
        </div>

        <!-- Order Summary -->
        <div class="card border border-surface-200">
          <div class="font-semibold text-lg mb-4 flex items-center gap-2">
            <i class="pi pi-calculator text-primary"></i>
            Tổng kết đơn hàng
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-surface-600">Tổng tiền hàng:</span>
              <span class="font-medium">{{ formatCurrency(orderData?.tongTienHang || 0) }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-surface-600">Giảm giá voucher:</span>
              <span class="font-medium text-green-600">-{{ formatCurrency(orderData?.giaTriGiamGiaVoucher || 0) }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-surface-600">Phí vận chuyển:</span>
              <span class="font-medium">{{ formatCurrency(orderData?.phiVanChuyen || 0) }}</span>
            </div>
            <div class="border-t border-surface-200 pt-3">
              <div class="flex justify-between items-center">
                <span class="font-semibold text-lg">Tổng thanh toán:</span>
                <span class="font-bold text-xl text-primary">{{ formatCurrency(orderData?.tongThanhToan || 0) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="card border border-surface-200" role="region" aria-label="Hành động đơn hàng">
          <div class="space-y-3">
            <Button
              label="Cập nhật đơn hàng"
              icon="pi pi-check"
              severity="success"
              size="large"
              class="w-full"
              @click="showUpdateConfirmation"
              :loading="updating"
              :disabled="!canUpdateOrder || updating"
              :aria-label="`Cập nhật đơn hàng ${orderData?.maHoaDon || ''}`"
              :aria-describedby="!canUpdateOrder ? 'update-validation-message' : null" />
            <div v-if="!canUpdateOrder" class="text-center" id="update-validation-message" role="alert" aria-live="polite">
              <small class="text-surface-500">
                <span v-if="!orderData?.sanPhamList?.length">Vui lòng thêm sản phẩm vào đơn hàng</span>
                <span v-else-if="orderData.giaohang && (!orderData.khachHang || !orderData.diaChiGiaoHang)">
                  Vui lòng chọn khách hàng và địa chỉ giao hàng
                </span>
                <span v-else-if="formValidationMessage">{{ formValidationMessage }}</span>
              </small>
            </div>
            <Button
              label="Hủy thay đổi"
              icon="pi pi-times"
              outlined
              severity="secondary"
              class="w-full"
              @click="cancelChanges"
              aria-label="Hủy thay đổi và quay về trang chi tiết đơn hàng" />
          </div>
        </div>
      </div>
    </div>

    <!-- Fast Customer Creation Dialog -->
    <FastCustomerCreate v-model:visible="fastCustomerDialogVisible" @customer-created="onCustomerCreated" />

    <!-- Fast Address Creation Dialog -->
    <FastAddressCreate v-model:visible="fastAddressDialogVisible" :customer="selectedCustomer"
      @address-created="onAddressCreated" />

    <!-- Product Variant Dialog -->
    <ProductVariantDialog ref="productVariantDialogRef" v-model:visible="variantDialogVisible"
      @variant-selected="addVariantToOrder" @request-cart-sync="syncCartWithDialog" />

    <!-- Price Change Warning -->
    <PriceChangeWarning v-model:visible="priceWarningVisible" :price-changes="cartPriceChanges"
      @acknowledge="acknowledgePriceChanges" />

    <!-- Voucher Suggestion Dialog -->
    <VoucherSuggestionDialog v-model:visible="voucherSuggestionVisible" :recommendations="voucherRecommendations"
      @voucher-selected="selectVoucher" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { useOrderStore } from '@/stores/orderStore'
import { useCustomerStore } from '@/stores/customerstore'


import { useCartReservations } from '@/composables/useCartReservations'
import { useEmbeddedAddress } from '@/composables/useEmbeddedAddress'
import { useVoucherMonitoring } from '@/composables/useVoucherMonitoring'
import voucherApi from '@/apis/voucherApi'

import { useOrderAudit } from '@/composables/useOrderAudit'
import { useOrderValidation } from '@/composables/useOrderValidation'
import { useRealTimeOrderManagement } from '@/composables/useRealTimeOrderManagement'
import { useRealTimePricing } from '@/composables/useRealTimePricing'



// PrimeVue Components
import Toast from 'primevue/toast'
import Button from 'primevue/button'
import Badge from 'primevue/badge'

import AutoComplete from 'primevue/autocomplete'
import Avatar from 'primevue/avatar'
import ToggleButton from 'primevue/togglebutton'


// Custom Components
import ProductVariantDialog from '@/views/orders/components/ProductVariantDialog.vue'
import FastCustomerCreate from '@/views/orders/components/FastCustomerCreate.vue'
import FastAddressCreate from '@/views/orders/components/FastAddressCreate.vue'
import PriceChangeWarning from '@/views/orders/components/PriceChangeWarning.vue'
import VoucherSuggestionDialog from '@/views/orders/components/VoucherSuggestionDialog.vue'

// Store access
const route = useRoute()
const router = useRouter()
const toast = useToast()
const orderStore = useOrderStore()
const customerStore = useCustomerStore()
const confirmDialog = inject('confirmDialog')

// Order data state
const loading = ref(true)
const updating = ref(false)
const error = ref(null)
const orderData = ref(null)

// Error boundary state
const criticalError = ref(null)
const hasRecoverableError = ref(false)

// Performance optimization - memoized computed properties
const orderSummary = computed(() => {
  if (!orderData.value) return null

  return {
    id: orderData.value.id,
    maHoaDon: orderData.value.maHoaDon,
    productCount: orderData.value.sanPhamList?.length || 0,
    totalAmount: orderData.value.tongThanhToan || 0,
    hasCustomer: !!orderData.value.khachHang,
    hasDelivery: !!orderData.value.diaChiGiaoHang,
    status: orderData.value.trangThai,
    paymentStatus: orderData.value.trangThaiThanhToan
  }
})

const isOrderLoaded = computed(() => !!orderData.value?.id)
const hasProducts = computed(() => (orderData.value?.sanPhamList?.length || 0) > 0)
const hasValidTotals = computed(() => (orderData.value?.tongThanhToan || 0) >= 0)

// Enhanced logging and debugging
const debugMode = ref(process.env.NODE_ENV === 'development')

const logComponentState = (action, additionalData = {}) => {
  try {
    if (!debugMode.value) return

    console.group(`🔧 OrderEdit Component - ${action}`)
    console.log('Order Data:', orderData.value)
    console.log('Loading State:', loading.value)
    console.log('Error State:', error.value)
    console.log('Updating State:', updating.value)
    console.log('Can Update Order:', canUpdateOrder?.value)
    console.log('Additional Data:', additionalData)
    console.groupEnd()
  } catch (err) {
    console.warn('Failed to log component state:', err)
  }
}

const logPerformanceMetric = (operation, startTime, additionalData = {}) => {
  const duration = Date.now() - startTime
  console.log(`⚡ Performance - ${operation}: ${duration}ms`, additionalData)

  // Log to audit system for monitoring
  logOrderModification(orderData.value?.id || 'unknown', `Performance metric: ${operation}`, {
    duration,
    operation,
    ...additionalData
  })
}

// Component verification and testing (defensive approach)
const verifyComponentIntegrity = () => {
  const warnings = []
  const criticalIssues = []

  // Check required functions (critical)
  if (typeof loadOrderData !== 'function') criticalIssues.push('loadOrderData function not defined')
  if (typeof updateOrder !== 'function') criticalIssues.push('updateOrder function not defined')

  // Check route parameters (critical)
  if (!route.params.id) criticalIssues.push('Order ID not found in route')

  // Check composables (warnings only - they might load asynchronously)
  if (!validateOrderData) warnings.push('Order validation composable not loaded yet')
  if (!startVoucherMonitoring) warnings.push('Voucher monitoring composable not loaded yet')
  if (!subscribeToPriceUpdates) warnings.push('Real-time pricing composable not loaded yet')

  // Log warnings but don't fail
  if (warnings.length > 0) {
    console.warn('⚠️ Component integrity warnings (may resolve during initialization):', warnings)
  }

  // Only fail on critical issues
  if (criticalIssues.length > 0) {
    console.error('🚨 Critical component integrity issues:', criticalIssues)
    handleCriticalError(new Error(`Critical component issues: ${criticalIssues.join(', ')}`), 'component verification')
    return false
  }

  console.log('✅ Component integrity verified successfully')
  return true
}

// Component state
const selectedCustomer = ref(null)
const customerSuggestions = ref([])
const fastCustomerDialogVisible = ref(false)
const fastAddressDialogVisible = ref(false)
const variantDialogVisible = ref(false)
const productVariantDialogRef = ref(null)
const priceWarningVisible = ref(false)
const voucherSuggestionVisible = ref(false)
const cartPriceChanges = ref([])
const voucherRecommendations = ref([])

// Delivery and recipient state
const recipientInfo = ref({
  hoTen: '',
  soDienThoai: ''
})
const recipientNameSuggestions = ref([])
const recipientPhoneSuggestions = ref([])
const recipientErrors = ref({})
const searchingRecipient = ref(false)

// Voucher state
const selectedVoucher = ref(null)
const voucherSuggestions = ref([])
const loadingBestVoucher = ref(false)
const availableVouchers = ref([])
const showAllVouchers = ref(false)
const voucherDisplayLimit = ref(3)

// Delivery state
const giaohang = ref(false)

// Customer management state
const searchingCustomers = ref(false)

// Address management using composable (only destructure what we need)
const { validateAddress } = useEmbeddedAddress()

// Update existing dialog states to use the embedded address composable
// fastCustomerDialogVisible and fastAddressDialogVisible are already defined above
// productVariantDialogRef is already defined above

// Voucher monitoring composable (only destructure what we need)
const {
  startVoucherMonitoring,
  stopVoucherMonitoring
} = useVoucherMonitoring()

// Cart reservations composable (only what we need)
const { releaseSpecificItems } = useCartReservations()

// Real-time order management composable (only what we need)
const { isConnected: wsConnected } = useRealTimeOrderManagement()

// Real-time pricing composable (only what we need)
const {
  priceUpdates,
  subscribeToPriceUpdates,
  getLatestPriceForVariant,
  hasRecentPriceChange
} = useRealTimePricing()

// Order validation composable (only what we need)
const {
  validationErrors,
  validateOrderData,
  clearValidationErrors,
  hasValidationErrors
} = useOrderValidation()

// Order audit composable for business rule tracking
const {
  logOrderModification,
  logBusinessRuleViolation,
  logValidationFailure
} = useOrderAudit()

// Business logic and edit restrictions
const canUpdateOrder = computed(() => {
  if (!orderData.value) return false

  // Check order status restrictions
  if (orderData.value.trangThaiThanhToan === 'DA_THANH_TOAN') {
    return false
  }

  if (orderData.value.trangThai === 'DA_HUY') {
    return false
  }

  if (orderData.value.trangThai === 'HOAN_THANH') {
    return false
  }

  // Check if order is in a state that allows editing
  const editableStatuses = ['CHO_XAC_NHAN', 'DA_XAC_NHAN', 'DANG_XU_LY']
  if (!editableStatuses.includes(orderData.value.trangThai)) {
    return false
  }

  // Basic form validation
  const hasProducts = orderData.value.sanPhamList?.length > 0
  const deliveryValid = !orderData.value.giaohang ||
    (orderData.value.giaohang && orderData.value.khachHang && orderData.value.diaChiGiaoHang)

  return hasProducts && deliveryValid
})

// Order status validation
const orderStatusRestriction = computed(() => {
  if (!orderData.value) return null

  if (orderData.value.trangThaiThanhToan === 'DA_THANH_TOAN') {
    return 'Không thể chỉnh sửa đơn hàng đã thanh toán'
  }

  if (orderData.value.trangThai === 'DA_HUY') {
    return 'Không thể chỉnh sửa đơn hàng đã hủy'
  }

  if (orderData.value.trangThai === 'HOAN_THANH') {
    return 'Không thể chỉnh sửa đơn hàng đã hoàn thành'
  }

  const editableStatuses = ['CHO_XAC_NHAN', 'DA_XAC_NHAN', 'DANG_XU_LY']
  if (!editableStatuses.includes(orderData.value.trangThai)) {
    return 'Đơn hàng đang ở trạng thái không cho phép chỉnh sửa'
  }

  return null
})

// Payment status validation
const paymentStatusRestriction = computed(() => {
  if (!orderData.value) return null

  if (orderData.value.trangThaiThanhToan === 'DA_THANH_TOAN') {
    return 'Đơn hàng đã được thanh toán không thể chỉnh sửa'
  }

  return null
})

// Business rule validation with audit logging
const validateBusinessRules = () => {
  if (!orderData.value) return false

  // Clear previous validation errors
  clearValidationErrors()

  // Validate order data using the composable
  const errors = validateOrderData(orderData.value)

  // Additional business rule checks with audit logging
  if (orderData.value.trangThaiThanhToan === 'DA_THANH_TOAN') {
    const violation = 'Attempted to edit paid order'
    errors.businessRule = ['Không thể chỉnh sửa đơn hàng đã thanh toán']
    logBusinessRuleViolation(orderData.value.id, violation, {
      paymentStatus: orderData.value.trangThaiThanhToan,
      orderStatus: orderData.value.trangThai
    })
  }

  if (orderData.value.trangThai === 'DA_HUY') {
    const violation = 'Attempted to edit cancelled order'
    errors.businessRule = ['Không thể chỉnh sửa đơn hàng đã hủy']
    logBusinessRuleViolation(orderData.value.id, violation, {
      orderStatus: orderData.value.trangThai
    })
  }

  if (orderData.value.trangThai === 'HOAN_THANH') {
    const violation = 'Attempted to edit completed order'
    errors.businessRule = ['Không thể chỉnh sửa đơn hàng đã hoàn thành']
    logBusinessRuleViolation(orderData.value.id, violation, {
      orderStatus: orderData.value.trangThai
    })
  }

  // Check if order is in editable status
  const editableStatuses = ['CHO_XAC_NHAN', 'DA_XAC_NHAN', 'DANG_XU_LY']
  if (!editableStatuses.includes(orderData.value.trangThai)) {
    const violation = 'Attempted to edit order in non-editable status'
    errors.businessRule = ['Đơn hàng đang ở trạng thái không cho phép chỉnh sửa']
    logBusinessRuleViolation(orderData.value.id, violation, {
      currentStatus: orderData.value.trangThai,
      editableStatuses
    })
  }

  // Validate product quantities against inventory
  if (orderData.value.sanPhamList?.length) {
    orderData.value.sanPhamList.forEach((item, index) => {
      if (item.soLuong <= 0) {
        errors[`sanPham_${index}_soLuong`] = ['Số lượng sản phẩm phải lớn hơn 0']
        logValidationFailure(orderData.value.id, 'Invalid product quantity', {
          productId: item.sanPhamChiTiet?.id,
          quantity: item.soLuong,
          index
        })
      }
    })
  }

  // Log validation results
  if (Object.keys(errors).length > 0) {
    logValidationFailure(orderData.value.id, 'Order validation failed', {
      errors: Object.keys(errors),
      errorCount: Object.keys(errors).length
    })
  }

  return Object.keys(errors).length === 0
}

// Form validation message with business rules
const businessRuleMessage = computed(() => {
  const statusRestriction = orderStatusRestriction.value
  const paymentRestriction = paymentStatusRestriction.value

  if (statusRestriction) return statusRestriction
  if (paymentRestriction) return paymentRestriction

  if (hasValidationErrors()) {
    const firstError = Object.values(validationErrors.value)[0]
    return Array.isArray(firstError) ? firstError[0] : firstError
  }

  return ''
})

// Computed property for displayed available vouchers
const displayedAvailableVouchers = computed(() => {
  if (showAllVouchers.value) {
    return availableVouchers.value
  }
  return availableVouchers.value.slice(0, voucherDisplayLimit.value)
})

// Connection status computed property
const connectionStatusText = computed(() => {
  if (wsConnected.value) {
    return 'Đã kết nối'
  } else {
    return 'Mất kết nối'
  }
})

const connectionStatusClass = computed(() => {
  return wsConnected.value ? 'text-green-600' : 'text-red-600'
})

const formValidationMessage = computed(() => {
  // Check business rule restrictions first
  const statusRestriction = orderStatusRestriction.value
  const paymentRestriction = paymentStatusRestriction.value

  if (statusRestriction) return statusRestriction
  if (paymentRestriction) return paymentRestriction

  // Check validation errors
  if (hasValidationErrors()) {
    const firstError = Object.values(validationErrors.value)[0]
    return Array.isArray(firstError) ? firstError[0] : firstError
  }

  // Basic form validation
  if (!orderData.value?.sanPhamList?.length) {
    return 'Vui lòng thêm sản phẩm vào đơn hàng'
  }
  if (orderData.value.giaohang && (!orderData.value.khachHang || !orderData.value.diaChiGiaoHang)) {
    return 'Vui lòng chọn khách hàng và địa chỉ giao hàng'
  }
  return ''
})

// Handle business rule violations with user feedback
const handleBusinessRuleViolation = (action) => {
  const restriction = orderStatusRestriction.value || paymentStatusRestriction.value

  if (restriction) {
    toast.add({
      severity: 'error',
      summary: 'Không thể thực hiện',
      detail: restriction,
      life: 5000
    })

    // Log the violation attempt
    logBusinessRuleViolation(orderData.value?.id, `Attempted ${action}`, {
      restriction,
      orderStatus: orderData.value?.trangThai,
      paymentStatus: orderData.value?.trangThaiThanhToan
    })

    return false
  }

  return true
}

// Placeholder computed properties (will be implemented in subsequent tasks)
const criticalExpiringOrders = computed(() => [])
const hasExpirationUpdates = computed(() => false)

// Utility functions
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount || 0)
}

const formatVariantName = (variant) => {
  if (!variant) return ''
  const attributes = []
  if (variant.mauSac) attributes.push(variant.mauSac)
  if (variant.kichThuoc) attributes.push(variant.kichThuoc)
  return attributes.join(' - ')
}

const getImageUrl = (imagePath) => {
  if (!imagePath) return null
  return storageApi.getImageUrl(imagePath)
}

const getCustomerDisplayLabel = (customer) => {
  if (!customer) return ''
  return `${customer.hoTen} - ${customer.soDienThoai}`
}

// Voucher management functions
const loadAvailableVouchers = async () => {
  if (!orderData.value) return

  try {
    const customerId = orderData.value.khachHang?.id || null
    const orderTotal = orderData.value.tongTienHang || 0

    const response = await voucherApi.getAvailableVouchers(customerId, orderTotal)

    if (response.success) {
      // Filter out already applied vouchers
      const appliedVoucherCodes = (orderData.value.voucherList || []).map(v => v.maPhieuGiamGia)
      availableVouchers.value = response.data.filter(
        voucher => !appliedVoucherCodes.includes(voucher.maPhieuGiamGia)
      )
    } else {
      availableVouchers.value = []
    }
  } catch (error) {
    console.error('Error loading available vouchers:', error)
    availableVouchers.value = []
  }
}

// Voucher application function
const selectVoucher = async (voucher) => {
  if (!orderData.value) return

  // Check business rules before allowing voucher changes
  if (!handleBusinessRuleViolation('apply voucher')) {
    return
  }

  try {
    const customerId = orderData.value.khachHang?.id || null
    const orderTotal = orderData.value.tongTienHang || 0

    const response = await voucherApi.validateVoucher(voucher.maPhieuGiamGia, customerId, orderTotal)

    if (response.success && response.data.valid) {
      // Check if voucher is already applied
      const existingVoucher = (orderData.value.voucherList || []).find(
        v => v.maPhieuGiamGia === voucher.maPhieuGiamGia
      )

      if (existingVoucher) {
        toast.add({
          severity: 'warn',
          summary: 'Cảnh báo',
          detail: 'Voucher này đã được áp dụng',
          life: 3000
        })
        return
      }

      // Add voucher to order with validated discount amount
      const voucherData = {
        ...response.data.voucher,
        giaTriGiam: response.data.discountAmount
      }

      if (!orderData.value.voucherList) {
        orderData.value.voucherList = []
      }
      orderData.value.voucherList.push(voucherData)
      calculateTotals()

      // Remove from available vouchers list
      availableVouchers.value = availableVouchers.value.filter(
        v => v.maPhieuGiamGia !== voucher.maPhieuGiamGia
      )

      toast.add({
        severity: 'success',
        summary: 'Thành công',
        detail: `Áp dụng voucher ${voucher.maPhieuGiamGia} thành công`,
        life: 3000
      })
    } else {
      toast.add({
        severity: 'error',
        summary: 'Lỗi',
        detail: response.data.error || 'Voucher không hợp lệ',
        life: 3000
      })
    }
  } catch (error) {
    console.error('Error applying voucher:', error)
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể áp dụng voucher. Vui lòng thử lại.',
      life: 3000
    })
  }
}

// Customer management functions will be added to replace placeholder functions

// Order data loading function
const loadOrderData = async () => {
  const orderId = route.params.id
  if (!orderId) {
    error.value = 'ID đơn hàng không hợp lệ'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = null

    const order = await orderStore.fetchOrderById(orderId)
    if (!order) {
      error.value = 'Không tìm thấy đơn hàng'
      return
    }

    // Check if order can be edited with comprehensive business rule validation
    if (order.trangThaiThanhToan === 'DA_THANH_TOAN') {
      error.value = 'Không thể chỉnh sửa đơn hàng đã thanh toán'
      logBusinessRuleViolation(order.id, 'Attempted to edit paid order', {
        paymentStatus: order.trangThaiThanhToan,
        orderStatus: order.trangThai
      })
      return
    }

    if (order.trangThai === 'DA_HUY') {
      error.value = 'Không thể chỉnh sửa đơn hàng đã hủy'
      logBusinessRuleViolation(order.id, 'Attempted to edit cancelled order', {
        orderStatus: order.trangThai
      })
      return
    }

    if (order.trangThai === 'HOAN_THANH') {
      error.value = 'Không thể chỉnh sửa đơn hàng đã hoàn thành'
      logBusinessRuleViolation(order.id, 'Attempted to edit completed order', {
        orderStatus: order.trangThai
      })
      return
    }

    // Check if order is in editable status
    const editableStatuses = ['CHO_XAC_NHAN', 'DA_XAC_NHAN', 'DANG_XU_LY']
    if (!editableStatuses.includes(order.trangThai)) {
      error.value = 'Đơn hàng đang ở trạng thái không cho phép chỉnh sửa'
      logBusinessRuleViolation(order.id, 'Attempted to edit order in non-editable status', {
        currentStatus: order.trangThai,
        editableStatuses
      })
      return
    }

    // Initialize form with order data
    await initializeEditForm(order)

  } catch (err) {
    console.error('Error loading order:', err)

    // Enhanced error handling with recovery options
    if (err.message?.includes('network') || err.message?.includes('timeout')) {
      handleRecoverableError(err, 'loading order data', 'Vui lòng kiểm tra kết nối mạng và thử lại.')
      error.value = `Lỗi kết nối: ${err.message}`
    } else if (err.message?.includes('not found') || err.message?.includes('404')) {
      error.value = 'Không tìm thấy đơn hàng'
    } else if (err.message?.includes('unauthorized') || err.message?.includes('403')) {
      error.value = 'Bạn không có quyền truy cập đơn hàng này'
    } else {
      handleCriticalError(err, 'loadOrderData')
      error.value = err.message || 'Không thể tải dữ liệu đơn hàng'
    }
  } finally {
    loading.value = false
  }
}

// Form initialization function
const initializeEditForm = async (order) => {
  try {
    // Initialize form data with order information
    orderData.value = {
      id: order.id,
      maHoaDon: order.maHoaDon,
      loaiHoaDon: order.loaiHoaDon,
      khachHang: order.khachHang,
      diaChiGiaoHang: order.diaChiGiaoHang,
      giaohang: !!order.diaChiGiaoHang,
      sanPhamList: order.chiTiet?.map(item => ({
        id: item.id,
        sanPhamChiTiet: item.sanPhamChiTiet,
        soLuong: item.soLuong,
        donGia: item.donGia,
        thanhTien: item.thanhTien,
        serialNumber: item.serialNumber
      })) || [],
      voucherList: order.voucherList || [],
      tongTienHang: order.tongTienHang || 0,
      giaTriGiamGiaVoucher: order.giaTriGiamGiaVoucher || 0,
      phiVanChuyen: order.phiVanChuyen || 0,
      tongThanhToan: order.tongThanhToan || 0,
      trangThai: order.trangThai,
      trangThaiThanhToan: order.trangThaiThanhToan
    }

    // Set selected customer for autocomplete
    if (order.khachHang) {
      selectedCustomer.value = order.khachHang
      // Load available vouchers for the customer
      await loadAvailableVouchers()
    }

    // Initialize recipient info if delivery address exists
    if (order.diaChiGiaoHang) {
      recipientInfo.value = {
        hoTen: order.diaChiGiaoHang.hoTen || order.khachHang?.hoTen || '',
        soDienThoai: order.diaChiGiaoHang.soDienThoai || order.khachHang?.soDienThoai || ''
      }
    }

    // Initialize delivery toggle state
    giaohang.value = !!order.diaChiGiaoHang

    // Ensure orderData.giaohang is synchronized with giaohang
    orderData.value.giaohang = giaohang.value

    console.log('Form initialized with order data:', orderData.value)
  } catch (err) {
    console.error('Error initializing form:', err)
    throw err
  }
}

const onDeliveryToggle = () => {
  if (!orderData.value.giaohang) {
    orderData.value.diaChiGiaoHang = null
    recipientInfo.value = { hoTen: '', soDienThoai: '' }
  }
  calculateTotals()
}

// Address management functions
const selectDeliveryAddress = (address) => {
  orderData.value.diaChiGiaoHang = address
  // Update recipient info with address data
  recipientInfo.value = {
    hoTen: address.hoTen || orderData.value.khachHang?.hoTen || '',
    soDienThoai: address.soDienThoai || orderData.value.khachHang?.soDienThoai || ''
  }
}

const searchRecipientByName = (event) => {
  // Will be implemented in Task 5
  console.log('searchRecipientByName - to be implemented', event)
}

const onRecipientNameSelect = (event) => {
  // Will be implemented in Task 5
  console.log('onRecipientNameSelect - to be implemented', event)
}

const searchRecipientByPhone = (event) => {
  // Will be implemented in Task 5
  console.log('searchRecipientByPhone - to be implemented', event)
}

const onRecipientPhoneSelect = (event) => {
  // Will be implemented in Task 5
  console.log('onRecipientPhoneSelect - to be implemented', event)
}

const searchVouchers = (event) => {
  // Will be implemented in Task 7
  console.log('searchVouchers - to be implemented', event)
}

const onVoucherSelect = (event) => {
  // Will be implemented in Task 7
  console.log('onVoucherSelect - to be implemented', event)
}

const removeVoucher = async (index) => {
  if (!orderData.value || !orderData.value.voucherList) return

  // Check business rules before allowing voucher removal
  if (!handleBusinessRuleViolation('remove voucher')) {
    return
  }

  const removedVoucher = orderData.value.voucherList[index]

  // Remove voucher directly
  orderData.value.voucherList.splice(index, 1)
  calculateTotals()

  // Reload available vouchers to include the removed voucher
  await loadAvailableVouchers()

  toast.add({
    severity: 'info',
    summary: 'Thông báo',
    detail: `Đã gỡ voucher ${removedVoucher.maPhieuGiamGia}`,
    life: 3000
  })
}

const findBestVoucher = async () => {
  if (!orderData.value || !orderData.value.khachHang) {
    toast.add({
      severity: 'warn',
      summary: 'Cảnh báo',
      detail: 'Vui lòng chọn khách hàng trước',
      life: 3000
    })
    return
  }

  try {
    await loadAvailableVouchers()

    if (availableVouchers.value.length === 0) {
      toast.add({
        severity: 'info',
        summary: 'Thông báo',
        detail: 'Không có voucher khả dụng',
        life: 3000
      })
      return
    }

    // Find the voucher with the highest discount value
    const bestVoucher = availableVouchers.value.reduce((best, current) => {
      const currentDiscount = calculateVoucherDiscount(current)
      const bestDiscount = calculateVoucherDiscount(best)
      return currentDiscount > bestDiscount ? current : best
    })

    if (bestVoucher) {
      await selectVoucher(bestVoucher)
    }
  } catch (error) {
    console.error('Error finding best voucher:', error)
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể tìm voucher tốt nhất',
      life: 3000
    })
  }
}

// Calculate voucher discount amount
const calculateVoucherDiscount = (voucher) => {
  if (!voucher || !orderData.value) return 0

  const orderTotal = orderData.value.tongTienHang || 0

  // Check minimum order value requirement
  if (voucher.giaTriDonHangToiThieu && orderTotal < voucher.giaTriDonHangToiThieu) {
    return 0
  }

  let discountAmount = 0

  if (voucher.loaiGiamGia === 'PHAN_TRAM') {
    // Percentage discount
    discountAmount = (orderTotal * voucher.giaTriGiam) / 100

    // Apply maximum discount limit if exists
    if (voucher.giaTriGiamToiDa && discountAmount > voucher.giaTriGiamToiDa) {
      discountAmount = voucher.giaTriGiamToiDa
    }
  } else {
    // Fixed amount discount
    discountAmount = voucher.giaTriGiam

    // Don't exceed order total
    if (discountAmount > orderTotal) {
      discountAmount = orderTotal
    }
  }

  return discountAmount
}

// Toggle voucher display function
const toggleVoucherDisplay = () => {
  showAllVouchers.value = !showAllVouchers.value
}

const showProductSelectionDialog = () => {
  // Open the ProductVariantDialog for product selection
  variantDialogVisible.value = true
}

// Product management functions
const addVariantToOrder = (variantData) => {
  console.log('addVariantToOrder called with:', variantData)
  console.log('Current orderData.value:', orderData.value)
  console.log('Current sanPhamList:', orderData.value?.sanPhamList)

  if (!orderData.value) {
    console.error('orderData.value is null or undefined')
    return
  }

  // Check business rules before allowing product addition
  if (!handleBusinessRuleViolation('add product')) {
    return
  }

  const { sanPhamChiTiet, soLuong, donGia, thanhTien, groupInfo } = variantData

  // Enhanced duplicate check matching OrderCreate.vue logic
  const existingIndex = orderData.value.sanPhamList.findIndex(item => {
    if (item.sanPhamChiTiet?.id !== sanPhamChiTiet.id) {
      return false
    }

    // If both items have serial numbers, compare them
    if (sanPhamChiTiet.serialNumber && item.sanPhamChiTiet?.serialNumber) {
      return item.sanPhamChiTiet.serialNumber === sanPhamChiTiet.serialNumber
    }

    // If both items have serial number IDs, compare them
    if (sanPhamChiTiet.serialNumberId && item.sanPhamChiTiet?.serialNumberId) {
      return item.sanPhamChiTiet.serialNumberId === sanPhamChiTiet.serialNumberId
    }

    // If neither has serial numbers, then it's a duplicate variant
    if (!sanPhamChiTiet.serialNumber && !sanPhamChiTiet.serialNumberId &&
      !item.sanPhamChiTiet?.serialNumber && !item.sanPhamChiTiet?.serialNumberId) {
      return true
    }

    // Different serial numbers or one has serial and other doesn't = not duplicate
    return false
  })

  if (existingIndex !== -1) {
    const serialInfo = sanPhamChiTiet.serialNumber ? ` (Serial: ${sanPhamChiTiet.serialNumber})` : ''
    console.log('Variant already exists at index:', existingIndex)
    toast.add({
      severity: 'warn',
      summary: 'Cảnh báo',
      detail: `Phiên bản này${serialInfo} đã có trong đơn hàng`,
      life: 3000
    })
    return
  }

  // Create new item with complete data structure matching OrderCreate.vue
  const newItem = {
    sanPhamChiTiet: {
      ...sanPhamChiTiet,
      // Ensure serial number data is properly included
      serialNumber: sanPhamChiTiet.serialNumber,
      serialNumberId: sanPhamChiTiet.serialNumberId
    },
    soLuong,
    donGia,
    thanhTien,
    // Store serial number at item level for easy access
    serialNumber: sanPhamChiTiet.serialNumber,
    groupInfo: groupInfo // Store group info for display purposes
  }

  console.log('Adding new item:', newItem)
  orderData.value.sanPhamList.push(newItem)
  console.log('Updated sanPhamList:', orderData.value.sanPhamList)

  calculateTotals()
  console.log('After calculateTotals - tongTienHang:', orderData.value.tongTienHang)

  // Reserve inventory for the added item
  try {
    reserveForCart([{
      sanPhamChiTietId: sanPhamChiTiet.id,
      soLuong: soLuong
    }])
  } catch (error) {
    console.warn('Failed to reserve inventory:', error)
  }

  // Sync with product variant dialog to update stock counts
  syncCartWithDialog()

  // Don't show individual success messages when adding from groups
  if (!groupInfo?.isFromGroup) {
    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: 'Đã thêm sản phẩm vào đơn hàng',
      life: 3000
    })
  }
}

// Enhanced sync cart data with product variant dialog for real-time inventory synchronization
const syncCartWithDialog = () => {
  const cartItems = orderData.value?.sanPhamList || []

  console.log(`🔄 [SYNC DEBUG] syncCartWithDialog called:`, {
    hasDialogRef: !!productVariantDialogRef.value,
    hasCartItems: !!orderData.value?.sanPhamList,
    cartItemsCount: cartItems.length,
    cartSerials: cartItems.map((item) => item.sanPhamChiTiet?.serialNumber).filter(Boolean),
  })

  if (productVariantDialogRef.value && orderData.value?.sanPhamList) {
    // Pass current cart data for immediate UI updates
    // This triggers cache invalidation and real-time sync in ProductVariantDialog
    productVariantDialogRef.value.updateUsedSerialNumbers(orderData.value.sanPhamList)
    console.log(`🔄 [SYNC DEBUG] Cart sync completed with ${cartItems.length} items`)

    // Note: Real-time inventory checking is now handled within ProductVariantDialog
    // via the backend API and cache invalidation to ensure accuracy
  } else {
    console.log(`⚠️ [SYNC DEBUG] Cannot sync - dialog ref or cart items not available`)
  }
}

const editProduct = (item, index) => {
  // Open ProductVariantDialog in edit mode for the specific item
  if (productVariantDialogRef.value) {
    productVariantDialogRef.value.editExistingItem(item, index)
    variantDialogVisible.value = true
  }
}

const removeProduct = (index) => {
  if (orderData.value?.sanPhamList && index >= 0 && index < orderData.value.sanPhamList.length) {
    const removedItem = orderData.value.sanPhamList[index]
    orderData.value.sanPhamList.splice(index, 1)

    // Recalculate totals after removal
    calculateTotals()

    // Sync with dialog to update inventory counts
    syncCartWithDialog()

    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: 'Đã xóa sản phẩm khỏi đơn hàng',
      life: 3000
    })

    console.log('Removed product:', removedItem)
  }
}

// Quantity update function
const updateProductQuantity = (index, newQuantity) => {
  if (orderData.value?.sanPhamList && index >= 0 && index < orderData.value.sanPhamList.length) {
    const item = orderData.value.sanPhamList[index]

    // Validate quantity
    if (newQuantity <= 0) {
      toast.add({
        severity: 'warn',
        summary: 'Cảnh báo',
        detail: 'Số lượng phải lớn hơn 0',
        life: 3000
      })
      return
    }

    // Update quantity and recalculate line total
    item.soLuong = newQuantity
    item.thanhTien = item.soLuong * item.donGia

    // Recalculate order totals
    calculateTotals()

    console.log('Updated product quantity:', { index, newQuantity, item })
  }
}

// Helper function to get product variant display information
const getVariantDisplayInfo = (item) => {
  if (!item?.sanPhamChiTiet) return ''

  const parts = []

  // Add color information
  if (item.sanPhamChiTiet.mauSac) parts.push(item.sanPhamChiTiet.mauSac.moTaMauSac)

  // Add storage information (handle different property names)
  const storage = item.sanPhamChiTiet.boNho || item.sanPhamChiTiet.bonho || item.sanPhamChiTiet.oCung || item.sanPhamChiTiet.ocung
  if (storage) parts.push(storage.moTaBoNho || storage.moTaOCung)

  // Add screen information
  if (item.sanPhamChiTiet.manHinh) parts.push(item.sanPhamChiTiet.manHinh.moTaManHinh)

  return parts.join(' • ')
}

const searchCustomers = async (event) => {
  try {
    searchingCustomers.value = true
    console.log('Searching customers with query:', event.query)

    // Try backend search first
    try {
      const customers = await customerStore.fetchCustomers({ search: event.query })
      console.log('Customer search results from backend:', customers)
      customerSuggestions.value = customers
      return
    } catch (backendError) {
      console.warn('Backend search failed, falling back to frontend filtering:', backendError)
    }

    // Fallback: Load all customers and filter on frontend
    const allCustomers = await customerStore.fetchCustomers()
    console.log('All customers loaded:', allCustomers)

    if (!event.query || event.query.trim() === '') {
      customerSuggestions.value = allCustomers.slice(0, 10) // Limit to first 10
      return
    }

    const query = event.query.toLowerCase().trim()
    const filteredCustomers = allCustomers.filter(customer => {
      return (
        customer.hoTen?.toLowerCase().includes(query) ||
        customer.soDienThoai?.includes(query) ||
        customer.email?.toLowerCase().includes(query) ||
        customer.maNguoiDung?.toLowerCase().includes(query)
      )
    }).slice(0, 10) // Limit to first 10 results

    console.log('Filtered customers:', filteredCustomers)
    customerSuggestions.value = filteredCustomers

  } catch (error) {
    console.error('Error searching customers:', error)
    customerSuggestions.value = []
  } finally {
    searchingCustomers.value = false
  }
}

const onCustomerSelect = async (event) => {
  try {
    console.log('Customer selected from search:', event.value)

    // Fetch complete customer data with addresses to ensure we have all necessary information
    const customerWithAddresses = await customerStore.fetchCustomerById(event.value.id)
    console.log('Customer data with addresses loaded:', customerWithAddresses)

    orderData.value.khachHang = customerWithAddresses
    orderData.value.diaChiGiaoHang = null // Reset delivery address when customer changes
    selectedCustomer.value = customerWithAddresses

    // Load available vouchers for the selected customer
    await loadAvailableVouchers()
  } catch (error) {
    console.error('Error loading customer details:', error)
    // Fallback to the basic customer data from search
    console.log('Using fallback customer data:', event.value)
    orderData.value.khachHang = event.value
    orderData.value.diaChiGiaoHang = null // Reset delivery address when customer changes
    selectedCustomer.value = event.value
    await loadAvailableVouchers()
  }
}

const clearCustomer = () => {
  orderData.value.khachHang = null
  orderData.value.diaChiGiaoHang = null
  selectedCustomer.value = null
  availableVouchers.value = []
  recipientInfo.value = { hoTen: '', soDienThoai: '' }
}

const onCustomerCreated = (customer) => {
  orderData.value.khachHang = customer
  selectedCustomer.value = customer
  fastCustomerDialogVisible.value = false
  toast.add({
    severity: 'success',
    summary: 'Thành công',
    detail: 'Đã tạo khách hàng mới',
    life: 3000
  })
}

const onAddressCreated = (address) => {
  // Refresh customer data to include new address
  if (orderData.value.khachHang) {
    orderData.value.khachHang.diaChis = orderData.value.khachHang.diaChis || []
    orderData.value.khachHang.diaChis.push(address)
    orderData.value.diaChiGiaoHang = address
  }
  fastAddressDialogVisible.value = false
  toast.add({
    severity: 'success',
    summary: 'Thành công',
    detail: 'Đã thêm địa chỉ giao hàng mới',
    life: 3000
  })
}

// onVariantSelected is replaced by addVariantToOrder function

const acknowledgePriceChanges = () => {
  // Clear price change warnings
  cartPriceChanges.value = []
  priceWarningVisible.value = false

  // Recalculate totals with latest prices
  calculateTotals()

  toast.add({
    severity: 'info',
    summary: 'Thông báo',
    detail: 'Đã cập nhật giá mới nhất cho đơn hàng',
    life: 3000
  })
}

// Real-time price monitoring functions
const checkForPriceChanges = async () => {
  if (!orderData.value?.sanPhamList?.length) return

  try {
    const priceChanges = []

    for (const item of orderData.value.sanPhamList) {
      if (item.sanPhamChiTiet?.id) {
        try {
          const latestPrice = await getLatestPriceForVariant(item.sanPhamChiTiet.id)

          if (latestPrice && latestPrice !== item.donGia) {
            priceChanges.push({
              productName: item.sanPhamChiTiet.sanPham?.tenSanPham || 'Sản phẩm',
              variantInfo: getVariantDisplayInfo(item),
              oldPrice: item.donGia,
              newPrice: latestPrice,
              quantity: item.soLuong,
              oldTotal: item.donGia * item.soLuong,
              newTotal: latestPrice * item.soLuong
            })
          }
        } catch (itemError) {
          console.warn(`Failed to get price for variant ${item.sanPhamChiTiet.id}:`, itemError)
        }
      }
    }

    if (priceChanges.length > 0) {
      cartPriceChanges.value = priceChanges
      priceWarningVisible.value = true
    }
  } catch (error) {
    handleRealTimeError(error, 'price change checking')
  }
}

// Apply latest prices to order items
const applyLatestPrices = async () => {
  if (!orderData.value?.sanPhamList?.length) return

  try {
    let hasChanges = false

    for (const item of orderData.value.sanPhamList) {
      if (item.sanPhamChiTiet?.id) {
        try {
          const latestPrice = await getLatestPriceForVariant(item.sanPhamChiTiet.id)

          if (latestPrice && latestPrice !== item.donGia) {
            item.donGia = latestPrice
            hasChanges = true
          }
        } catch (itemError) {
          console.warn(`Failed to update price for variant ${item.sanPhamChiTiet.id}:`, itemError)
        }
      }
    }

    if (hasChanges) {
      calculateTotals()
    }
  } catch (error) {
    handleRealTimeError(error, 'price application')
  }
}

// Real-time error handling and recovery
const handleRealTimeError = (error, context = 'real-time operation') => {
  console.error(`Real-time error in ${context}:`, error)

  // Show user-friendly error message
  toast.add({
    severity: 'error',
    summary: 'Lỗi kết nối',
    detail: 'Có lỗi xảy ra với kết nối thời gian thực. Đang thử kết nối lại...',
    life: 5000
  })

  // Attempt to recover by resubscribing to price updates
  if (orderData.value?.sanPhamList?.length) {
    setTimeout(() => {
      try {
        const variantIds = orderData.value.sanPhamList
          .map(item => item.sanPhamChiTiet?.id)
          .filter(Boolean)

        if (variantIds.length > 0) {
          subscribeToPriceUpdates(variantIds)
        }
      } catch (retryError) {
        console.error('Failed to resubscribe to price updates:', retryError)
      }
    }, 5000) // Retry after 5 seconds
  }
}

// onVoucherSelected is replaced by selectVoucher function

const updateOrder = async () => {
  try {
    updating.value = true
    const updateStartTime = Date.now()

    // Validate business rules before attempting update
    if (!validateBusinessRules()) {
      toast.add({
        severity: 'warn',
        summary: 'Không thể cập nhật',
        detail: businessRuleMessage.value || 'Vui lòng kiểm tra lại thông tin đơn hàng',
        life: 5000
      })
      return
    }

    // Check if order can be updated
    if (!canUpdateOrder.value) {
      handleBusinessRuleViolation('update order')
      return
    }

    console.log('updateOrder called')
    console.log('orderData.value:', orderData.value)
    console.log('sanPhamList:', orderData.value.sanPhamList)

    // Validate that we have products to update
    if (!orderData.value.sanPhamList || orderData.value.sanPhamList.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Cảnh báo',
        detail: 'Đơn hàng phải có ít nhất một sản phẩm',
        life: 3000
      })
      return
    }

    // Prepare update data following OrderEdit1.vue patterns
    const updateData = {
      khachHangId: orderData.value.khachHang?.id || null,
      diaChiGiaoHangId: orderData.value.diaChiGiaoHang?.id || null,
      nguoiNhanTen: orderData.value.khachHang?.hoTen || null,
      nguoiNhanSdt: orderData.value.khachHang?.soDienThoai || null,
      chiTiet: orderData.value.sanPhamList.map(item => {
        const chiTietItem = {
          sanPhamChiTietId: item.sanPhamChiTiet?.id,
          soLuong: item.soLuong,
          donGia: item.donGia,
          thanhTien: item.thanhTien,
          // Ensure serial number data is properly mapped
          serialNumberId: item.sanPhamChiTiet?.serialNumberId || item.serialNumberId,
          serialNumber: item.sanPhamChiTiet?.serialNumber || item.serialNumber
        }

        // Only include ID for existing items (items that were already in the order)
        if (item.id) {
          chiTietItem.id = item.id
        }

        console.log('Mapping chiTiet item:', chiTietItem)
        console.log('Source item:', item)
        return chiTietItem
      }),
      tongTienHang: orderData.value.tongTienHang,
      giaTriGiamGiaVoucher: orderData.value.giaTriGiamGiaVoucher,
      phiVanChuyen: orderData.value.phiVanChuyen,
      tongThanhToan: orderData.value.tongThanhToan,
      voucherCodes: (orderData.value.voucherList || []).map(voucher => voucher.maPhieuGiamGia)
    }

    console.log('updateData being sent to API:', updateData)
    console.log('Number of chiTiet items:', updateData.chiTiet.length)
    console.log('Total amount being sent:', updateData.tongThanhToan)

    // Log the update attempt
    logOrderModification(orderData.value.id, 'Order update initiated', {
      productCount: updateData.chiTiet.length,
      totalAmount: updateData.tongThanhToan,
      hasCustomer: !!updateData.khachHangId,
      hasDelivery: !!updateData.diaChiGiaoHangId
    })

    const result = await orderStore.updateOrder(orderData.value.id, updateData)

    if (result) {
      console.log('Update result received:', result)
      console.log('Result chiTiet length:', result.chiTiet?.length || 0)
      console.log('Result tongThanhToan:', result.tongThanhToan)

      // Check if the backend properly processed the update
      const hasValidProducts = result.chiTiet && result.chiTiet.length > 0
      const hasValidTotals = result.tongThanhToan > 0 || updateData.tongThanhToan === 0

      if (hasValidProducts && hasValidTotals) {
        // Log successful update with detailed audit trail
        logOrderModification(orderData.value.id, 'Order update completed successfully', {
          resultProductCount: result.chiTiet?.length || 0,
          resultTotalAmount: result.tongThanhToan,
          updateDuration: Date.now() - updateStartTime,
          changedFields: {
            products: updateData.chiTiet.length,
            customer: updateData.khachHangId,
            delivery: updateData.diaChiGiaoHangId,
            vouchers: updateData.voucherCodes.length,
            totals: {
              subtotal: updateData.tongTienHang,
              discount: updateData.giaTriGiamGiaVoucher,
              shipping: updateData.phiVanChuyen,
              total: updateData.tongThanhToan
            }
          },
          apiResponse: {
            success: true,
            dataIntegrity: hasValidProducts && hasValidTotals
          }
        })

        toast.add({
          severity: 'success',
          summary: 'Thành công',
          detail: 'Đã cập nhật đơn hàng thành công',
          life: 3000
        })

        // Update local order data with result
        orderData.value = result

        // Redirect back to order detail with success message
        router.push({
          path: `/orders/${orderData.value.id}`,
          query: { updated: 'true' }
        })
      } else if (!hasValidProducts) {
        console.error('Backend did not process chiTiet correctly')
        console.error('Expected chiTiet items:', updateData.chiTiet.length)
        console.error('Received chiTiet items:', result.chiTiet?.length || 0)

        logBusinessRuleViolation(orderData.value.id, 'Backend processing error - invalid product data', {
          expectedProducts: updateData.chiTiet.length,
          receivedProducts: result.chiTiet?.length || 0
        })

        toast.add({
          severity: 'error',
          summary: 'Lỗi Backend',
          detail: 'Backend không xử lý đúng thông tin sản phẩm. Vui lòng kiểm tra lại hoặc liên hệ hỗ trợ.',
          life: 7000
        })
      } else {
        console.warn('Update succeeded but result validation failed:', {
          hasValidProducts,
          hasValidTotals,
          resultTotals: result.tongThanhToan,
          expectedTotals: updateData.tongThanhToan
        })

        logValidationFailure(orderData.value.id, 'Update result validation failed', {
          hasValidProducts,
          hasValidTotals,
          resultTotals: result.tongThanhToan,
          expectedTotals: updateData.tongThanhToan
        })

        toast.add({
          severity: 'warn',
          summary: 'Cảnh báo',
          detail: 'Đơn hàng đã được cập nhật nhưng có thể có vấn đề với dữ liệu. Vui lòng kiểm tra lại.',
          life: 5000
        })

        // Still redirect but with warning
        router.push({
          path: `/orders/${orderData.value.id}`,
          query: { updated: 'warning' }
        })
      }
    } else {
      throw new Error('No result returned from update operation')
    }
  } catch (error) {
    console.error('Error updating order:', error)

    // Log the error
    logValidationFailure(orderData.value?.id, 'Order update failed', {
      error: error.message,
      stack: error.stack
    })

    // Show error with retry option
    const errorMessage = error.message || 'Vui lòng thử lại.'
    const isNetworkError = error.message?.includes('network') || error.message?.includes('timeout')

    toast.add({
      severity: 'error',
      summary: 'Lỗi cập nhật đơn hàng',
      detail: `Không thể cập nhật đơn hàng: ${errorMessage}${isNetworkError ? ' Kiểm tra kết nối mạng và thử lại.' : ''}`,
      life: 7000
    })

    // For network errors, offer retry option
    if (isNetworkError) {
      setTimeout(() => {
        toast.add({
          severity: 'info',
          summary: 'Thử lại?',
          detail: 'Nhấn nút "Cập nhật đơn hàng" để thử lại',
          life: 5000
        })
      }, 2000)
    }
  } finally {
    updating.value = false
  }
}

// Update confirmation dialog
const showUpdateConfirmation = async () => {
  if (!canUpdateOrder.value) {
    toast.add({
      severity: 'warn',
      summary: 'Cảnh báo',
      detail: 'Vui lòng hoàn tất thông tin đơn hàng trước khi cập nhật',
      life: 3000
    })
    return
  }

  // Show detailed confirmation dialog using specialized template
  const confirmed = await confirmDialog.showOrderUpdateConfirm(orderData.value)

  if (!confirmed) return

  updateOrder()
}

// Safe page reload function
const safePageReload = () => {
  try {
    if (typeof window !== 'undefined' && window.location && window.location.reload) {
      window.location.reload()
    } else {
      // Fallback: navigate to current route
      router.go(0)
    }
  } catch (err) {
    console.error('Failed to reload page:', err)
    // Last resort: navigate to orders list
    router.push('/orders')
  }
}

// Global error boundary handler
const handleCriticalError = (error, context = 'Unknown') => {
  console.error(`Critical error in ${context}:`, error)

  criticalError.value = {
    message: error.message || 'An unexpected error occurred',
    context,
    timestamp: new Date().toISOString(),
    stack: error.stack
  }

  // Log critical error for monitoring (safe call)
  try {
    if (typeof logValidationFailure === 'function') {
      logValidationFailure(orderData.value?.id || 'unknown', `Critical error in ${context}`, {
        error: error.message,
        stack: error.stack,
        context,
        orderState: orderData.value ? 'loaded' : 'not-loaded'
      })
    } else {
      console.warn('logValidationFailure function not available')
    }
  } catch (logError) {
    console.warn('Failed to log validation failure:', logError)
  }

  toast.add({
    severity: 'error',
    summary: 'Lỗi nghiêm trọng',
    detail: `Đã xảy ra lỗi nghiêm trọng trong ${context}. Vui lòng tải lại trang.`,
    life: 10000
  })
}

// Recoverable error handler
const handleRecoverableError = (error, context = 'Unknown', recovery = null) => {
  console.warn(`Recoverable error in ${context}:`, error)

  hasRecoverableError.value = true

  toast.add({
    severity: 'warn',
    summary: 'Cảnh báo',
    detail: `Đã xảy ra lỗi trong ${context}. ${recovery || 'Hệ thống sẽ tự động thử lại.'}`,
    life: 5000
  })

  // Auto-clear recoverable error flag after 10 seconds
  setTimeout(() => {
    hasRecoverableError.value = false
  }, 10000)
}

const cancelChanges = () => {
  // Navigate back to order detail without saving changes
  router.push(`/orders/${orderData.value?.id || route.params.id}`)
}

// Calculate order totals
const calculateTotals = () => {
  if (!orderData.value) return

  // Calculate subtotal from products
  const tongTienHang = orderData.value.sanPhamList.reduce((total, item) => {
    return total + (item.donGia * item.soLuong)
  }, 0)

  // Calculate voucher discount
  const giaTriGiamGiaVoucher = orderData.value.voucherList.reduce((total, voucher) => {
    return total + (voucher.giaTriGiam || 0)
  }, 0)

  // Calculate final total
  const tongThanhToan = tongTienHang - giaTriGiamGiaVoucher + (orderData.value.phiVanChuyen || 0)

  // Update order data
  orderData.value.tongTienHang = tongTienHang
  orderData.value.giaTriGiamGiaVoucher = giaTriGiamGiaVoucher
  orderData.value.tongThanhToan = tongThanhToan
}

// Watch for order data changes to recalculate totals
watch(() => orderData.value?.sanPhamList, () => {
  if (orderData.value) {
    calculateTotals()
  }
}, { deep: true })

watch(() => orderData.value?.voucherList, () => {
  if (orderData.value) {
    calculateTotals()
  }
}, { deep: true })

// Synchronize giaohang with orderData.giaohang
watch(giaohang, (newValue) => {
  if (orderData.value) {
    orderData.value.giaohang = newValue
  }
})

watch(() => orderData.value?.giaohang, (newValue) => {
  if (newValue !== giaohang.value) {
    giaohang.value = newValue
  }
})

// Initialize component
onMounted(async () => {
  const startTime = Date.now()

  try {
    logComponentState('Component mounted', { orderId: route.params.id })

    // Optional component integrity verification (non-blocking)
    try {
      verifyComponentIntegrity()
    } catch (verifyError) {
      console.warn('Component verification failed, but continuing initialization:', verifyError)
    }

    await loadOrderData()

  // Validate business rules after loading order data
  if (orderData.value) {
    const isValid = validateBusinessRules()
    if (!isValid) {
      console.warn('Order loaded but business rule validation failed')
    }

    // Log successful order loading for audit
    logOrderModification(orderData.value.id, 'Order loaded for editing', {
      orderStatus: orderData.value.trangThai,
      paymentStatus: orderData.value.trangThaiThanhToan,
      productCount: orderData.value.sanPhamList?.length || 0,
      totalAmount: orderData.value.tongThanhToan
    })
  }

  // Start voucher monitoring for real-time updates (defensive)
  if (orderData.value?.khachHang?.id && typeof startVoucherMonitoring === 'function') {
    try {
      startVoucherMonitoring(orderData.value.khachHang.id, orderData.value.tongTienHang || 0)
    } catch (err) {
      console.warn('Failed to start voucher monitoring:', err)
    }
  }

  // Subscribe to real-time price updates (defensive)
  if (orderData.value?.sanPhamList?.length && typeof subscribeToPriceUpdates === 'function') {
    try {
      const variantIds = orderData.value.sanPhamList
        .map(item => item.sanPhamChiTiet?.id)
        .filter(Boolean)

      if (variantIds.length > 0) {
        subscribeToPriceUpdates(variantIds)
      }
    } catch (error) {
      console.warn('Failed to subscribe to price updates:', error)
      if (typeof handleRealTimeError === 'function') {
        handleRealTimeError(error, 'price subscription setup')
      }
    }
  }

  // Set up periodic price checking
  const priceCheckInterval = setInterval(checkForPriceChanges, 30000) // Check every 30 seconds

  // Store interval ID for cleanup
  if (typeof window !== 'undefined') {
    window.priceCheckInterval = priceCheckInterval
  }

  logPerformanceMetric('Component initialization', startTime, {
    hasOrder: !!orderData.value?.id,
    productCount: orderData.value?.sanPhamList?.length || 0
  })

  } catch (err) {
    handleCriticalError(err, 'component initialization')
  }
})

// Cleanup on component unmount (defensive)
onUnmounted(() => {
  // Stop voucher monitoring safely
  if (typeof stopVoucherMonitoring === 'function') {
    try {
      stopVoucherMonitoring()
    } catch (err) {
      console.warn('Failed to stop voucher monitoring during cleanup:', err)
    }
  }

  // Clean up price check interval
  if (typeof window !== 'undefined' && window.priceCheckInterval) {
    clearInterval(window.priceCheckInterval)
    delete window.priceCheckInterval
  }

  // Release cart reservations
  if (orderData.value?.sanPhamList?.length) {
    const items = orderData.value.sanPhamList.map(item => ({
      sanPhamChiTietId: item.sanPhamChiTiet?.id,
      soLuong: item.soLuong
    })).filter(item => item.sanPhamChiTietId)

    if (items.length > 0) {
      releaseSpecificItems(items)
    }
  }
})

// Watch for customer changes to restart voucher monitoring (defensive)
watch(() => orderData.value?.khachHang?.id, (newCustomerId, oldCustomerId) => {
  if (newCustomerId && newCustomerId !== oldCustomerId) {
    // Restart voucher monitoring with new customer
    if (typeof stopVoucherMonitoring === 'function') {
      try {
        stopVoucherMonitoring()
      } catch (err) {
        console.warn('Failed to stop voucher monitoring:', err)
      }
    }

    if (typeof startVoucherMonitoring === 'function') {
      try {
        startVoucherMonitoring(newCustomerId, orderData.value?.tongTienHang || 0)
      } catch (err) {
        console.warn('Failed to restart voucher monitoring:', err)
      }
    }

    // Reload available vouchers for new customer
    if (typeof loadAvailableVouchers === 'function') {
      try {
        loadAvailableVouchers()
      } catch (err) {
        console.warn('Failed to reload available vouchers:', err)
      }
    }
  }
})

// Watch for price updates to trigger warnings
watch(priceUpdates, (newUpdates) => {
  if (newUpdates && newUpdates.length > 0 && orderData.value?.sanPhamList?.length) {
    // Check if any price updates affect current order items
    const affectedItems = orderData.value.sanPhamList.filter(item =>
      newUpdates.some(update => update.variantId === item.sanPhamChiTiet?.id)
    )

    if (affectedItems.length > 0) {
      checkForPriceChanges()
    }
  }
}, { deep: true })

// Watch for connection status changes
watch(wsConnected, (isConnected) => {
  if (isConnected) {
    toast.add({
      severity: 'success',
      summary: 'Kết nối',
      detail: 'Đã kết nối lại với máy chủ',
      life: 3000
    })
  } else {
    toast.add({
      severity: 'warn',
      summary: 'Mất kết nối',
      detail: 'Mất kết nối với máy chủ. Một số tính năng có thể bị hạn chế.',
      life: 5000
    })
  }
})
</script>

<style scoped>
.order-create-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
</style>
