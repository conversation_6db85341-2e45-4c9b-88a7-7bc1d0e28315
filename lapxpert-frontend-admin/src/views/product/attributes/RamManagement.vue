<template>
  <GenericAttributeManager :config="ramConfig" />
</template>

<script setup>
import GenericAttributeManager from './GenericAttributeManager.vue'

const ramConfig = {
  type: 'ram',
  label: 'RAM',
  title: '<PERSON><PERSON><PERSON>n lý RAM',
  description: '<PERSON><PERSON>ản lý danh sách RAM trong hệ thống',
  tableTitle: 'Danh sách RAM',
  dialogTitle: 'Thông tin RAM',
  icon: 'pi pi-server',
  fieldName: 'moTaRam',
  fieldLabel: '<PERSON>ô tả RAM',
  codeFieldName: 'maRam'
}
</script>
