<template>
  <GenericAttributeManager :config="categoryConfig" />
</template>

<script setup>
import GenericAttributeManager from './GenericAttributeManager.vue'

const categoryConfig = {
  type: 'category',
  label: '<PERSON><PERSON> mục',
  title: '<PERSON>uản lý <PERSON> mục',
  description: 'Quản lý danh sách danh mục sản phẩm trong hệ thống',
  tableTitle: '<PERSON>h sách <PERSON> mụ<PERSON>',
  dialogTitle: 'Thông tin <PERSON> mục',
  icon: 'pi pi-sitemap',
  fieldName: 'moTaDanhMuc',
  fieldLabel: '<PERSON><PERSON> tả <PERSON> mục',
  codeFieldName: 'maDanh<PERSON>uc'
}
</script>
