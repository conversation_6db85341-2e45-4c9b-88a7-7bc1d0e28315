<template>
  <GenericAttributeManager :config="brandConfig" />
</template>

<script setup>
import GenericAttributeManager from './GenericAttributeManager.vue'

const brandConfig = {
  type: 'brand',
  label: 'Thương hiệu',
  title: '<PERSON>u<PERSON><PERSON> lý Thương hiệu',
  description: '<PERSON>u<PERSON>n lý danh sách thương hiệu sản phẩm trong hệ thống',
  tableTitle: 'Dan<PERSON> sách Thương hiệu',
  dialogTitle: 'Thông tin Thương hiệu',
  icon: 'pi pi-tag',
  fieldName: 'moTaThu<PERSON><PERSON><PERSON>',
  fieldLabel: '<PERSON><PERSON> tả Thương hiệu',
  codeFieldName: 'maThu<PERSON><PERSON><PERSON>'
}
</script>
