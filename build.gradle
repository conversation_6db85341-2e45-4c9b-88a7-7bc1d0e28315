plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.4'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.lapxpert'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

repositories {
	mavenCentral()
}

ext {
	mapstructVersion = "1.5.5.Final"
}

dependencies {
	// Spring Boot Starters
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-data-rest'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.springframework.boot:spring-boot-starter-websocket'
	implementation 'org.springframework.boot:spring-boot-starter-data-redis'
	implementation 'org.springframework.boot:spring-boot-starter-batch'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'me.paulschwarz:spring-dotenv:4.0.0'
	implementation 'org.springframework.boot:spring-boot-starter-mail'
	implementation 'org.springframework.boot:spring-boot-starter-webflux'

	// Jackson JSR310 module for Java 8 time support (explicit dependency for WebSocket Redis serialization)
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

	// Redisson for distributed locking
	implementation 'org.redisson:redisson-spring-boot-starter:3.50.0'

	runtimeOnly 'org.postgresql:postgresql'
	implementation 'org.liquibase:liquibase-core'
	implementation 'io.hypersistence:hypersistence-utils-hibernate-60:3.9.4'

	implementation 'io.jsonwebtoken:jjwt:0.9.1'

	implementation 'io.minio:minio:8.5.17'
	implementation 'org.apache.poi:poi:5.4.1'
	implementation 'org.apache.poi:poi-ooxml:5.4.1'
	implementation 'com.itextpdf:itext-core:9.2.0'
	implementation 'com.itextpdf:html2pdf:6.2.0'

	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	implementation "org.mapstruct:mapstruct:${mapstructVersion}"
	annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
	implementation 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
	annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.batch:spring-batch-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	implementation 'com.google.code.gson:gson:2.13.1'
	implementation 'org.apache.logging.log4j:log4j-core:2.25.0'
	implementation 'org.apache.logging.log4j:log4j-api:2.25.0'

	// OkHttp for MoMo SDK
	implementation 'com.squareup.okhttp3:okhttp:4.12.0'
	implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

	// Apache HttpClient for enhanced HTTP connection management (Spring Boot 3.4 compatible)
	implementation 'org.apache.httpcomponents.client5:httpclient5'

	// Spring Retry for @Retryable annotation support
	implementation 'org.springframework.retry:spring-retry'
	implementation 'org.springframework:spring-aspects'
}

tasks.named('test') {
	useJUnitPlatform()
	jvmArgs '-XX:+EnableDynamicAgentLoading'
}